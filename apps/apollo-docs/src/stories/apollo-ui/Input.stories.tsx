import React, { useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import {
  Button,
  IconButton,
  Input,
  Modal,
  Select,
  Typography,
} from "@apollo/ui"
import { InfoCircle, Search } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * Input component
 *
 * The Input component provides a styled, accessible text input with optional label,
 * helper text, decorators, required indicator, error state, full‑width layout, and
 * character count support.
 *
 * Notes:
 * - Default size is now "medium";
 * - Available sizes: "small" | "medium".
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Input",
  component: Input,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2294-1987&m=dev",
    },
    docs: {
      description: {
        component:
          "The Input component renders a text input with Apollo design system styling. The default size is now 'large' (no 'medium' size).",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Input } from "@apollo/ui"`} language="tsx" />
          <h2 id="input-props">Props</h2>
          <ArgTypes />
          <h2 id="input-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Always use placeholder in input field",
              "Be clearly labeled so it’s obvious to users what they should enter into the field",
              "Helper text should be used to provide additional information or instructions.",
              "Only ask for information that’s really needed",
              "Inline error messages should be used to provide feedback on invalid input.",
              "Character or word counters should be used if there is a character or word limit.",
            ]}
          />
          <h2 id="input-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always use <code>label</code> prop to provide a text label for
                the input. The label should be descriptive and concise,
                indicating what information the user should enter."
              </>,
              <>
                Use the <code>placeholder`</code> prop to provide a short hint
                that describes the expected value of the input. The placeholder
                should be a short word or phrase that helps the user understand
                what to enter.
              </>,
              <>
                <code>labalDecorator</code> or <code>helperTextDecorator</code>{" "}
                should be used to provide additional information or
                instructions. "
              </>,
              <>
                Use the <code>helperText</code> prop to provide additional
                information or instructions. The helper text should be concise
                and to the point.
              </>,
              <>
                Provide descriptive error messages using <code>error</code> prop
                with <code>helperText</code> to help users understand and
                correct validation issues.
              </>,
              <>
                For required fields, use the <code>required</code> prop to
                ensure proper screen reader announcements and native browser
                validation.
              </>,
              <>
                Character counting with <code>hasCharacterCount</code> with{" "}
                <code>maxLength</code> prop automatically provides accessibility
                labels for screen readers.
              </>,
            ]}
          />
          <h2 id="input-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Input component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloInput-fieldRoot",
                description: "Styles applied to the field wrapper element",
                usageNotes: "Use for overall field styling including label and helper text positioning",
              },
              {
                cssClassName: ".ApolloInput-controlRoot",
                description: "Styles applied to the input control container",
                usageNotes: "Contains the input element and decorators, handles border and background",
              },
              {
                cssClassName: ".ApolloInput-startDecorator",
                description: "Styles applied to the start decorator element",
                usageNotes: "Use for styling icons or content placed before the input",
              },
              {
                cssClassName: ".ApolloInput-endDecorator",
                description: "Styles applied to the end decorator element",
                usageNotes: "Use for styling icons or content placed after the input",
              },
            ]}
          />
          <h2 id="input-examples">Examples</h2>
          <Stories title="" />
          <h2 id="input-dos-donts">
            Do’s and Don’ts
          </h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input label="Name" placeholder="Enter your name" />
                    </div>
                  ),
                  description: "Keep the placeholder for input hints only",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input
                        label="Name"
                        placeholder="Must be at least 8 characters"
                      />
                    </div>
                  ),
                  description:
                    "Do not misuse placeholder as helper text, use helperText instead.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input label="Email" placeholder="<EMAIL>" />
                    </div>
                  ),
                  description:
                    "Make sure your input has a short, descriptive label above it that describes what the user should type into the box below.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input
                        label="What is your email address? We'll use this to contact you."
                        placeholder="<EMAIL>"
                      />
                    </div>
                  ),
                  description:
                    "Avoid phrasing your labels as questions. Keep in mind that your label shouldn’t contain instructions. Reserve those for the helper text.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input label="Email" placeholder="<EMAIL>" />
                    </div>
                  ),
                  description:
                    "Use the help text description to convey requirements or to show any formatting examples that would help user comprehension.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input
                        label="Email"
                        placeholder="<EMAIL>"
                        helperText="For example: <EMAIL>. We'll use this to contact you. Please provide a valid email address."
                      />
                    </div>
                  ),
                  description:
                    "Helper text should be 2 lines or less and avoid repeating the field label. If the field label provides sufficient context for completing the action, then you likely don’t need to add help text.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input
                        label="Password"
                        placeholder="Enter your password"
                        helperText={
                          <ul style={{ margin: 0, paddingLeft: 16 }}>
                            <Typography level="labelMedium" color="primary">
                              <li> Contains one uppercase letter</li>
                            </Typography>
                            <Typography level="labelMedium" color="primary">
                              <li>Contains one lowercase letter</li>
                            </Typography>
                            <Typography level="labelMedium" color="primary">
                              <li>Contains one number </li>
                            </Typography>
                            <Typography level="labelMedium" color="danger">
                              <li>
                                Contains one special character (@, #, $, %,
                                etc.)
                              </li>
                            </Typography>
                          </ul>
                        }
                      />
                    </div>
                  ),
                  description:
                    "Bullet points helper text are suitable for inputs with multiple conditions, critical form fields where mistakes are costly, or inputs users often misunderstand.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Input
                        label="Email"
                        placeholder="<EMAIL>"
                        helperText={
                          <div>
                            <Typography level="bodySmall">
                              Set password for your account.
                            </Typography>
                            <ul style={{ margin: 0, paddingLeft: 16 }}>
                              <Typography level="bodySmall" color="danger">
                                <li>
                                  Contains one special character (@, #, $, %,
                                  etc.)
                                </li>
                              </Typography>
                            </ul>
                          </div>
                        }
                      />
                    </div>
                  ),
                  description:
                    "Do not display helper text together with inline error",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    size: {
      control: { type: "radio" },
      options: ["small", "medium"],
      description: "Visual size of the input. Default is 'medium'.",
      table: {
        type: { summary: '"small" | "medium"' },
        defaultValue: { summary: "medium" },
      },
    },
    label: {
      control: { type: "text" },
      description: "Accessible label displayed above the input.",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper or error text displayed below the input.",
    },
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text for the input element.",
    },
    error: {
      control: { type: "boolean" },
      description:
        "When true, shows error styles and treats helperText as an error message.",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the input.",
    },
    required: {
      control: { type: "boolean" },
      description:
        "Marks the field as required and shows an asterisk in the label.",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Stretches the input to the full width of its container.",
    },
    hasCharacterCount: {
      control: { type: "boolean" },
      description:
        "Displays a character count in the helper area; pair with maxLength.",
    },
    maxLength: {
      control: { type: "number" },
      description:
        "Maximum number of characters allowed in the input (enables counter when hasCharacterCount is true).",
      table: { type: { summary: "number" } },
    },
    minLength: {
      control: { type: "number" },
      description:
        "Minimum value (for inputs like type='number' or date-related).",
      table: { type: { summary: "number" } },
    },
    pattern: {
      control: { type: "text" },
      description: "Regex pattern the input's value must match.",
      table: { type: { summary: "string" } },
    },

    startDecorator: {
      control: false,
      description:
        "Element displayed at the start of the input (icon or text).",
    },
    endDecorator: {
      control: false,
      description: "Element displayed at the end of the input (icon or text).",
    },
    labelDecorator: {
      control: { type: "text" },
      description:
        "Element displayed alongside the label (e.g., badge, helper chip, or icon). Accepts ReactNode.",
      table: { type: { summary: "ReactNode" } },
    },
    helperTextDecorator: {
      control: { type: "text" },
      description:
        "Element displayed alongside the helper text (e.g., info icon, suffix). Accepts ReactNode.",
      table: { type: { summary: "ReactNode" } },
    },
    onChange: {
      control: false,
      description: "Callback fired when the value changes.",
      table: {
        type: {
          summary: "(event: React.ChangeEvent<HTMLInputElement>) => void",
        },
      },
    },
    rootRef: {
      control: false,
      description: "Ref for the root element.",
      table: { type: { summary: "Ref<HTMLDivElement>" } },
    },
    rootProps: {
      control: false,
      description: "Props for the root element.",
      table: { type: { summary: "HTMLAttributes<HTMLDivElement>" } },
    },
    fieldProps: {
      control: false,
      description: "Props for the field element.",
      table: { type: { summary: "FieldProps" } },
    },
  },
  args: {
    label: undefined,
    helperText: undefined,
    placeholder: "Placeholder text",
    error: false,
    disabled: false,
    required: false,
    fullWidth: false,
    hasCharacterCount: false,
  },
} satisfies Meta<typeof Input>

export default meta

type Story = StoryObj<typeof Input>

/** Default Input (demonstrates default size 'large') */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Input without explicit size prop. The component defaults to size 'large'.",
      },
    },
  },
  args: {
    label: "Name",
    helperText: "Helper text",
    hasCharacterCount: true,
    maxLength: 10,
  },
}

/** Input with different sizes (small, large) */
export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: "Showcases both available sizes: small and large (default).",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "flex-start" }}>
      <div>
        <Input {...args} size="small" label="Small" placeholder="Small size" />
      </div>
      <div>
        <Input {...args} label="Medium (Default)" placeholder="Medium size" />
      </div>
    </div>
  ),
}

/** Input with full width */
export const FullWidth: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Input stretches to fill the width of its container.",
      },
    },
  },
  render: (args) => (
    <div style={{ width: "100%" }}>
      <Input {...args} fullWidth label="Full width" />
    </div>
  ),
}

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A side-by-side comparison of common Input states for quick visual reference: default, disabled, error, decorators, helper text, and with character count.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "end",
        }}
      >
        <Input placeholder="Default" />
        <Input
          label="Name"
          labelDecorator={<InfoCircle size={12} />}
          placeholder="With field label & label decorator"
        />
        <Input
          helperText="Helper text"
          helperTextDecorator={<InfoCircle size={12} />}
          placeholder="With helper text & helper text decorator"
        />
        <Input
          hasCharacterCount
          maxLength={10}
          placeholder="With count character"
        />
        <Input disabled placeholder="Disabled with placeholder" />
        <Input disabled value={"Disabled with value"} />
        <Input
          startDecorator={<Search size={16} />}
          placeholder="With start decorators"
        />
        <Input endDecorator="⌘K" placeholder="With end decorators" />
        <Input error helperText="Error message" placeholder="With error" />
        <Input
          error
          helperText="Error message"
          helperTextDecorator={<InfoCircle size={12} />}
          placeholder="With error & helper text decorator"
        />
      </div>
    )
  },
}

/** Input with label */
export const Label: Story = {
  parameters: {
    docs: {
      description: {
        story: "Input with label and label decorator.",
      },
    },
  },
  render: () => {
    function Demo() {
      const [open, setOpen] = useState(false)
      const close = () => setOpen(false)
      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "center",
          }}
        >
          <Typography level="bodyLarge">Default</Typography>
          <Input label="Label" placeholder="Placeholder text" />
          <Typography level="bodyLarge">Label Decorator (Clickable)</Typography>
          <Input
            label="Label"
            placeholder="Placeholder text"
            labelDecorator={
              <IconButton
                type="button"
                onClick={() => setOpen(true)}
                style={{
                  padding: 0,
                  background: "none",
                  height: "fit-content",
                  minHeight: "fit-content",
                  width: "fit-content",
                  minWidth: "fit-content",
                }}
                aria-label="More info"
                size="small"
              >
                <InfoCircle size={12} />
              </IconButton>
            }
          />

          <Modal.Root
            open={open}
            onOpenChange={(o) => setOpen(!!o)}
            dismissible
          >
            <Modal.Header>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={close}>Button</Button>
            </Modal.Footer>
            <Modal.CloseButton />
          </Modal.Root>
        </div>
      )
    }
    return <Demo />
  },
}

/** Input with helper text */
export const HelperText: Story = {
  parameters: {
    docs: {
      description: {
        story: "Input with helper text and helper text decorator.",
      },
    },
  },
  render: () => {
    function Demo() {
      const [open, setOpen] = useState(false)
      const close = () => setOpen(false)
      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "center",
          }}
        >
          <Typography level="bodyLarge">Default</Typography>
          <Input
            label="Label"
            helperText="Helper text"
            placeholder="Placeholder text"
          />
          <Typography level="bodyLarge">With long helper text</Typography>
          <Input
            label="Label"
            placeholder="Placeholder text"
            error
            helperText={
              <Typography level="labelLarge" color="danger">
                You can not use the name
                <Typography
                  level="labelLarge"
                  color="danger"
                  onClick={() => setOpen(true)}
                  style={{
                    cursor: "pointer",
                    textDecoration: "underline",
                    marginLeft: "4px",
                  }}
                >
                  show detail
                </Typography>
              </Typography>
            }
          />
          <Modal.Root
            open={open}
            onOpenChange={(o) => setOpen(!!o)}
            dismissible
          >
            <Modal.Header>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={close}>Button</Button>
            </Modal.Footer>
            <Modal.CloseButton />
          </Modal.Root>
        </div>
      )
    }
    return <Demo />
  },
}

/** Input with validation */
export const Validation: Story = {
  parameters: {
    docs: {
      description: {
        story: "Input with label, helper text, and validation states.",
      },
    },
  },
  args: {
    label: "Name",
    helperText: "Validate me",
    error: true,
  },
}

export const InputInForm: Story = {
  parameters: {
    docs: {
      description: {
        story: "Input used within a form.",
      },
    },
  },
  render: () => {
    return (
      <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
        <Typography level="titleMedium">Create Issue</Typography>
        <form
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 20,
            maxWidth: 500,
          }}
        >
          <Input label="Name" placeholder="Enter your name" />
          <Input
            label="Email"
            placeholder="Enter your email"
            helperText="We'll only use this for account purposes."
          />
        </form>
      </div>
    )
  },
}

/** Input with required field indicator */
export const RequiredInput: Story = {
  parameters: {
    docs: {
      description: { story: "Shows the required asterisk next to the label." },
    },
  },
  args: {
    label: "Email Address",
    placeholder: "Enter your email",
    required: true,
  },
}

/** Inline table-like with Input */
export const InlineFormTableLike: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A compact, table-like layout combining Select and Input in a single row with numeric cells and helper text.",
      },
    },
  },
  render: () => {
    const border = "1px solid #E5E7EB" // light gray border
    const headerCell = {
      padding: "10px 12px",
      background: "#F3F4F6",
      borderRight: border,
      fontWeight: 500,
      whiteSpace: "nowrap" as const,
    }
    const cell = {
      padding: "8px 12px",
      borderRight: border,
    }
    return (
      <div style={{ width: 860 }}>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "150px 220px 120px 220px 150px",
            border: border,
            borderRadius: 8,
            overflow: "hidden",
          }}
        >
          {/* Header */}
          <div style={headerCell}>ชื่อสินค้า</div>
          <div style={headerCell}>ประเภทหมวด</div>
          <div style={headerCell}>ราคาทุนเดิม</div>
          <div style={headerCell}>ราคาทุนโปร</div>
          <div style={{ ...headerCell, borderRight: "none" }}>ราคาขายโปร</div>

          {/* Row */}
          <div style={cell}>
            <Typography level="bodyMedium">แอมบิเพอร์</Typography>
            <Typography level="bodySmall" color="tertiary">
              เจลลาเวนเดอร์
            </Typography>
          </div>
          <div style={cell}>
            <Select fullWidth placeholder="Please select">
              <Select.Option label="ประเภท A" value="a" />
              <Select.Option label="ประเภท B" value="b" />
              <Select.Option label="ประเภท C" value="c" />
            </Select>
          </div>
          <div style={cell}>
            <Typography level="bodyLarge">0</Typography>
          </div>
          <div style={cell}>
            <Input placeholder="Type here" helperText="Helper text" />
          </div>
          <div style={{ ...cell, borderRight: "none" }}>
            <Typography level="bodyLarge">0</Typography>
          </div>
        </div>
      </div>
    )
  },
}
