@layer legacy {
    .field {
        --apl-field-gap: var(--apl-space-gap-2xs, 4px);
        --apl-field-helper-color: var(--apl-colors-content-description, #5C6372);
        --apl-field-helper-error-color: var(--apl-colors-content-danger-default, #E11919);
        --apl-field-label-required-symbol-color: var(--apl-colors-content-danger-default, #E11919);
        --apl-field-label-required-symbol-ml: var(--apl-space-gap-2xs, 4px);
        --apl-field-label-gap: var(--apl-space-gap-2xs, 4px);
    }
}

@layer apollo {
    .field {
        --apl-field-gap: var(--apl-alias-spacing-gap-gap3, 4px);
        --apl-field-label-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        --apl-field-helper-root-gap: var(--apl-alias-spacing-gap-gap5, 8px);
        --apl-field-helper-container-gap: var(--apl-alias-spacing-gap-gap4, 6px);
        --apl-field-helper-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
        --apl-field-helper-error-color: var(--apl-alias-color-error-error, #C0000B);
        --apl-field-label-required-symbol-color: var(--apl-alias-color-error-error, #C0000B);
        --apl-field-label-required-symbol-ml: var(--apl-alias-spacing-gap-gap3, 4px);
        --apl-field-label-gap: var(--apl-alias-spacing-gap-gap3, 4px);
        --apl-field-helper-count-color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }
}

.fieldRoot {
    display: flex;
    min-width: 48px;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: var(--apl-field-gap);
    width: fit-content;
}

.fieldLabel {
    composes: apl-typography-caption apl-typography-label-medium from '../../base.module.css';
    color: var(--apl-field-label-color);
    display: flex;
    align-items: flex-start;
    align-self: stretch;
    gap: var(--apl-field-label-gap);
}

.fieldHelperTextRoot {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    align-self: stretch;
    gap: var(--apl-field-helper-root-gap);
}

.fieldHelperTextContainer {
    display: flex;
    align-items: flex-start;
    gap: var(--apl-field-helper-container-gap);
    flex: 1 0 0;
}

.fieldHelperText {
    composes: apl-typography-label-medium from '../../base.module.css';
    margin: 0;
    color: var(--apl-field-helper-color);
}

.fieldHelperTextCount {
    color: var(--apl-field-helper-count-color);
}

.fieldHelperTextError {
    color: var(--apl-field-helper-error-color);
}

.fieldLabelRequiredSymbol {
    color: var(--apl-field-label-required-symbol-color);
}

.fieldFullWidth {
    width: 100%;
}

.fieldLabelDecorator,
.fieldHelperTextDecorator {
    color: inherit;
    display: flex;
    align-items: center;
    padding: 2px;
    gap: 10px;
}