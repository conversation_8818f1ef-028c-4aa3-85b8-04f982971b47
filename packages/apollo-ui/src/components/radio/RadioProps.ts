/* eslint-disable @typescript-eslint/no-explicit-any */
import { ComponentProps } from "react"
import { Radio as BaseRadio } from "@base-ui-components/react/radio"
import { RadioGroup as BaseRadioGroup } from "@base-ui-components/react/radio-group"

export type RadioProps = {
  value: string
  disabled?: boolean
  readOnly?: boolean
  required?: boolean
  labelProps?: ComponentProps<"label">
} & Omit<BaseRadio.Root.Props, "value" | "disabled" | "readOnly" | "required">


export type RadioGroupProps<T = any> = {
  direction?: "horizontal" | "vertical"
  value?: T
  defaultValue?: T
  onValueChange?: (value: T, event: Event) => void
} & Omit<BaseRadioGroup.Props, "value" | "defaultValue" | "onValueChange">
