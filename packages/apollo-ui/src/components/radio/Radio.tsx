import { Radio as BaseRadio } from "@base-ui-components/react/radio"
import classNames from "classnames"

import { Typography } from "../typography"
import styles from "./radio.module.css"
import type { RadioProps } from "./RadioProps"

export const Radio = ({
  className,
  children,
  disabled,
  readOnly,
  required,
  labelProps,
  ...radioProps
}: RadioProps) => {
  return (
    <label
      {...labelProps}
      className={classNames(
        "ApolloRadio-label",
        styles.radio,
        styles.radioLabel,
        labelProps?.className
      )}
    >
      <BaseRadio.Root
        {...radioProps}
        disabled={disabled}
        readOnly={readOnly}
        required={required}
        className={classNames("ApolloRadio-root", styles.radioRoot, className)}
      >
        <BaseRadio.Indicator
          className={classNames("ApolloRadio-indicator", styles.indicator)}
        />
      </BaseRadio.Root>
      {typeof children === "string" ? (
        <Typography
          className={classNames("ApolloRadio-labelText", {
            [styles.labelTextDisabled]: disabled,
          })}
          level="bodyLarge"
        >
          {children}
        </Typography>
      ) : (
        children
      )}
    </label>
  )
}
