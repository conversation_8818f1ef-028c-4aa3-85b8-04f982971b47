"use client"

import { forwardRef, Fragment, useMemo, useState } from "react"
import classNames from "classnames"

import { getPageSizeOptions } from "../../utils"
import { DoubleLeft } from "../common/DoubleLeft"
import { DoubleRight } from "../common/DoubleRight"
import { Left } from "../common/Left"
import { Right } from "../common/Right"
import { IconButton } from "../icon-button"
import { Select } from "../select"
import { Typography } from "../typography"
import styles from "./pagination.module.css"
import { PaginationItem } from "./PaginationItem"
import type { PaginationProps } from "./PaginationProps"

type PaginationItemType = "separator" | "item"
type PaginationConfigItem = { type: PaginationItemType; page: number }

export const Pagination = forwardRef<HTMLDivElement, PaginationProps>(
  function Pagination(
    {
      onChange,
      page: controlledPage,
      minimumEdgeRange = 6,
      count = 1,
      defaultPage = 1,
      siblingCount = 1,
      boundaryCount = 3,
      minimumVisibleCount = 10,
      showFirstPageButton = true,
      showLastPageButton = true,
      showPrevPageButton = true,
      showNextPageButton = true,
      disabled,
      disabledFirstPageButton,
      disabledLastPageButton,
      disabledPrevPageButton,
      disabledNextPageButton,
      pageSize,
      pageSizeOptions,
      onPageSizeChange,
      displayType,
      className,
      ...navProps
    },
    ref
  ) {
    const [uncontrolledPage, setUncontrolledPage] = useState(defaultPage)

    const isControlled = controlledPage !== undefined
    const currentPage = isControlled ? controlledPage : uncontrolledPage

    const handlePageChange = (
      event: React.MouseEvent<HTMLButtonElement>,
      newPage: number
    ) => {
      if (!isControlled) {
        setUncontrolledPage(newPage)
      }
      if (onChange) {
        onChange(event, newPage)
      }
    }

    const handleClickPrevPage = (
      event: React.MouseEvent<HTMLButtonElement>
    ) => {
      const newPage = currentPage - 1
      handlePageChange(event, newPage < 1 ? 1 : newPage)
    }

    const handleClickNextPage = (
      event: React.MouseEvent<HTMLButtonElement>
    ) => {
      const newPage = currentPage + 1
      handlePageChange(event, newPage > currentCount ? currentCount : newPage)
    }

    const handleClickFirstPage = (
      event: React.MouseEvent<HTMLButtonElement>
    ) => {
      handlePageChange(event, 1)
    }

    const handleClickLastPage = (
      event: React.MouseEvent<HTMLButtonElement>
    ) => {
      handlePageChange(event, currentCount)
    }

    const currentCount = useMemo(() => {
      if (pageSize && typeof pageSize === "number") {
        return Math.ceil(count / pageSize)
      }
      return count
    }, [count, pageSize])

    const pageItems = useMemo(() => {
      const pages: number[] = []
      for (let i = 1; i <= currentCount; i++) {
        pages.push(i)
      }

      return pages
    }, [currentCount])

    const pageButtons = useMemo(
      () =>
        pageItems.reduce((allPages: PaginationConfigItem[], page: number) => {
          const beginBoundaryRange = boundaryCount
          const endBoundaryRange = currentCount - boundaryCount + 1
          const beginSiblingRange = currentPage - siblingCount
          const endSiblingRange = currentPage + siblingCount

          const isInFullRange = currentCount <= minimumVisibleCount
          const isBeginingRange =
            currentPage < minimumEdgeRange && page <= minimumEdgeRange
          const isEndingRange =
            currentPage > currentCount - minimumEdgeRange &&
            page >= currentCount - minimumEdgeRange
          const isInBoundaryRange =
            page <= beginBoundaryRange || page >= endBoundaryRange
          const isInSiblingRange =
            page <= endSiblingRange && page >= beginSiblingRange

          const isPageItem =
            isInFullRange || isInSiblingRange || isInBoundaryRange

          if (isPageItem || isBeginingRange || isEndingRange) {
            const lastPage = allPages?.[allPages?.length - 1]
            const additionalSeparator = []

            if (
              lastPage &&
              lastPage.type === "item" &&
              page - lastPage.page > 1
            ) {
              additionalSeparator.push({
                type: "separator" as PaginationItemType,
                page: page * -1,
              })
            }

            return [
              ...allPages,
              ...additionalSeparator,
              { type: "item" as PaginationItemType, page },
            ]
          }

          return allPages
        }, [] as PaginationConfigItem[]),
      [
        boundaryCount,
        currentCount,
        currentPage,
        minimumEdgeRange,
        minimumVisibleCount,
        pageItems,
        siblingCount,
      ]
    )

    return (
      <nav
        className={classNames(
          "ApolloPagination-root",
          styles.pagination,
          styles.paginationRoot,
          className
        )}
        ref={ref}
        {...navProps}
      >
        <div
          data-fixed-screen={displayType ? "true" : "false"}
          className={classNames(
            "ApolloPagination-full",
            styles.paginationFull,
            { [styles.paginationNotDisplayScreen]: displayType === "compact" }
          )}
        >
          {showPrevPageButton ? (
            <IconButton
              size="small"
              className={classNames(
                "ApolloPagination-prevPageButton",
                styles.paginationPrevButton
              )}
              disabled={disabled ?? disabledPrevPageButton ?? currentPage === 1}
              name="prev-button"
              onClick={handleClickPrevPage}
              color="primary"
            >
              <Left width={18} height={18} />
            </IconButton>
          ) : null}
          {pageButtons?.map((item) => (
            <Fragment key={item.page}>
              {item.type === "item" ? (
                <PaginationItem
                  disabled={disabled}
                  onClick={(event) =>
                    currentPage === item.page
                      ? undefined
                      : handlePageChange(event, item.page)
                  }
                  selected={currentPage === item.page}
                >
                  {item.page}
                </PaginationItem>
              ) : (
                <PaginationItem borderless className="pointer-events-none">
                  ...
                </PaginationItem>
              )}
            </Fragment>
          ))}
          {showNextPageButton ? (
            <IconButton
              size="small"
              className={classNames(
                "ApolloPagination-nextPageButton",
                styles.paginationNextButton
              )}
              disabled={
                disabled ??
                disabledNextPageButton ??
                currentPage === currentCount
              }
              name="next-button"
              onClick={handleClickNextPage}
            >
              <Right width={18} height={18} />
            </IconButton>
          ) : null}
        </div>
        <div
          data-fixed-screen={displayType ? "true" : "false"}
          className={classNames(
            "ApolloPagination-compact",
            styles.paginationCompact,
            {
              [styles.paginationDisplayScreen]: displayType === "compact",
            }
          )}
        >
          {showFirstPageButton ? (
            <IconButton
              size="small"
              className={classNames(
                "ApolloPagination-firstPageButton",
                styles.paginationFirstPageButton
              )}
              disabled={
                disabled ?? disabledFirstPageButton ?? currentPage === 1
              }
              name="first-page-button"
              onClick={handleClickFirstPage}
              color="primary"
            >
              <DoubleLeft width={18} height={18} />
            </IconButton>
          ) : null}
          {showPrevPageButton ? (
            <IconButton
              size="small"
              className={classNames(
                "ApolloPagination-prevPageButton",
                styles.paginationPrevButton
              )}
              disabled={disabled ?? disabledPrevPageButton ?? currentPage === 1}
              name="prev-button"
              onClick={handleClickPrevPage}
              color="primary"
            >
              <Left width={18} height={18} />
            </IconButton>
          ) : null}
          <Typography
            className={classNames(
              "ApolloPagination-pageInfo",
              styles.paginationPageInfo,
              { [styles.paginationPageInfoDisabled]: disabled }
            )}
            level="bodyMedium"
          >{`${currentPage} / ${pageButtons.length}`}</Typography>
          {showNextPageButton ? (
            <IconButton
              size="small"
              className={classNames(
                "ApolloPagination-nextPageButton",
                styles.paginationNextButton
              )}
              disabled={
                disabled ??
                disabledNextPageButton ??
                currentPage === currentCount
              }
              name="next-button"
              onClick={handleClickNextPage}
            >
              <Right width={18} height={18} />
            </IconButton>
          ) : null}
          {showLastPageButton ? (
            <IconButton
              size="small"
              className={classNames(
                "ApolloPagination-lastPageButton",
                styles.paginationLastPageButton
              )}
              disabled={
                disabled ??
                disabledLastPageButton ??
                currentPage === currentCount
              }
              name="last-page-button"
              onClick={handleClickLastPage}
            >
              <DoubleRight width={18} height={18} />
            </IconButton>
          ) : null}
        </div>
        {pageSize ? (
          <div
            className={classNames(
              "ApolloPagination-pageSize",
              styles.paginationPageSize
            )}
          >
            {typeof pageSize === "number" ? (
              <Select
                size="small"
                value={pageSize}
                onChange={onPageSizeChange}
                disabled={disabled}
              >
                {(pageSizeOptions && pageSizeOptions.length > 0
                  ? pageSizeOptions
                  : getPageSizeOptions(10, 5, count)
                )?.map((option) => (
                  <Select.Option
                    key={typeof option === "object" ? option.value : option}
                    label={
                      typeof option === "object"
                        ? option.label
                        : `${option}/page`
                    }
                    value={typeof option === "object" ? option.value : option}
                  />
                ))}
              </Select>
            ) : (
              pageSize
            )}
          </div>
        ) : null}
      </nav>
    )
  }
)
